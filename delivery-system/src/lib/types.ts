export interface DeliveryCompany {
  id: string;
  name: string;
  logo: string;
  isActive: boolean;
  apiKeys: {
    apiKey?: string;
    secretKey?: string;
    username?: string;
    password?: string;
    accountId?: string;
    [key: string]: string | undefined;
  };
  requiredKeys: {
    name: string;
    key: string;
    type: 'text' | 'password';
    placeholder: string;
  }[];
  features: {
    getPrices: boolean;
    getTrackingNumber: boolean;
    getShippingLabel: boolean;
  };
}

export interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  city: string;
  postalCode?: string;
  products: {
    name: string;
    quantity: number;
    price: number;
  }[];
  totalAmount: number;
  deliveryCompany?: string;
  deliveryStatus?: 'pending' | 'processing' | 'shipped' | 'delivered';
  trackingNumber?: string;
  shippingCost?: number;
  createdAt: string;
  updatedAt: string;
}

export interface ShippingLabel {
  orderId: string;
  deliveryCompany: string;
  trackingNumber: string;
  labelUrl?: string;
  labelData?: string;
  createdAt: string;
}export interface DeliveryCompany {
  id: string;
  name: string;
  logo: string;
  isActive: boolean;
  apiKeys: {
    apiKey?: string;
    secretKey?: string;
    username?: string;
    password?: string;
    accountId?: string;
    [key: string]: string | undefined;
  };
  requiredKeys: {
    name: string;
    key: string;
    type: 'text' | 'password';
    placeholder: string;
  }[];
  features: {
    getPrices: boolean;
    getTrackingNumber: boolean;
    getShippingLabel: boolean;
  };
}

export interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  city: string;
  postalCode?: string;
  products: {
    name: string;
    quantity: number;
    price: number;
  }[];
  totalAmount: number;
  deliveryCompany?: string;
  deliveryStatus?: 'pending' | 'processing' | 'shipped' | 'delivered';
  trackingNumber?: string;
  shippingCost?: number;
  createdAt: string;
  updatedAt: string;
}

export interface ShippingLabel {
  orderId: string;
  deliveryCompany: string;
  trackingNumber: string;
  labelUrl?: string;
  labelData?: string;
  createdAt: string;
}export interface DeliveryCompany {
  id: string;
  name: string;
  logo: string;
  isActive: boolean;
  apiKeys: {
    apiKey?: string;
    secretKey?: string;
    username?: string;
    password?: string;
    accountId?: string;
    [key: string]: string | undefined;
  };
  requiredKeys: {
    name: string;
    key: string;
    type: 'text' | 'password';
    placeholder: string;
  }[];
  features: {
    getPrices: boolean;
    getTrackingNumber: boolean;
    getShippingLabel: boolean;
  };
}

export interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  city: string;
  postalCode?: string;
  products: {
    name: string;
    quantity: number;
    price: number;
  }[];
  totalAmount: number;
  deliveryCompany?: string;
  deliveryStatus?: 'pending' | 'processing' | 'shipped' | 'delivered';
  trackingNumber?: string;
  shippingCost?: number;
  createdAt: string;
  updatedAt: string;
}

export interface ShippingLabel {
  orderId: string;
  deliveryCompany: string;
  trackingNumber: string;
  labelUrl?: string;
  labelData?: string;
  createdAt: string;
}