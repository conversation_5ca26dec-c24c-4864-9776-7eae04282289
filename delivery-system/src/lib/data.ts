import { DeliveryCompany, Order } from './types';

export const deliveryCompanies: DeliveryCompany[] = [
  {
    id: 'msm-go',
    name: 'MSM <PERSON>',
    logo: '/images/MSM GO.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      },
      {
        name: 'المفتاح السري',
        key: 'secretKey',
        type: 'password',
        placeholder: 'أدخل المفتاح السري'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'noest-express',
    name: 'NOEST Express',
    logo: '/images/NOEST Express.jpeg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'اسم المستخدم',
        key: 'username',
        type: 'text',
        placeholder: 'أدخل اسم المستخدم'
      },
      {
        name: 'كلمة المرور',
        key: 'password',
        type: 'password',
        placeholder: 'أدخل كلمة المرور'
      },
      {
        name: 'رقم الحساب',
        key: 'accountId',
        type: 'text',
        placeholder: 'أدخل رقم الحساب'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'rocket-delivery',
    name: 'ROCKET DELIVERY',
    logo: '/images/ROCKET DELIVERY.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: false
    }
  },
  {
    id: 'world-express',
    name: 'WORLD EXPRESS',
    logo: '/images/WORLD EXPRESS.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      },
      {
        name: 'المفتاح السري',
        key: 'secretKey',
        type: 'password',
        placeholder: 'أدخل المفتاح السري'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'yalidine',
    name: 'Yalidine',
    logo: '/images/Yalidine.jpeg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'رمز التاجر',
        key: 'merchantId',
        type: 'text',
        placeholder: 'أدخل رمز التاجر'
      },
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'zr-express',
    name: 'ZR Express',
    logo: '/images/ZR Express.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'اسم المستخدم',
        key: 'username',
        type: 'text',
        placeholder: 'أدخل اسم المستخدم'
      },
      {
        name: 'كلمة المرور',
        key: 'password',
        type: 'password',
        placeholder: 'أدخل كلمة المرور'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  }
];

// بيانات الطلبات النموذجية
export const sampleOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    customerName: 'أحمد محمد',
    customerPhone: '0501234567',
    customerAddress: 'شارع الملك فهد، حي العليا',
    city: 'الرياض',
    postalCode: '12345',
    products: [
      {
        name: 'قميص قطني',
        quantity: 2,
        price: 150
      },
      {
        name: 'بنطلون جينز',
        quantity: 1,
        price: 200
      }
    ],
    totalAmount: 500,
    deliveryStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    customerName: 'سارة عبدالله',
    customerPhone: '0567891234',
    customerAddress: 'شارع التحلية، حي السلامة',
    city: 'جدة',
    postalCode: '23456',
    products: [
      {
        name: 'فستان سهرة',
        quantity: 1,
        price: 350
      }
    ],
    totalAmount: 350,
    deliveryStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    orderNumber: 'ORD-003',
    customerName: 'خالد العتيبي',
    customerPhone: '0512345678',
    customerAddress: 'شارع الأمير سلطان، حي الخالدية',
    city: 'الدمام',
    postalCode: '34567',
    products: [
      {
        name: 'ساعة يد رجالية',
        quantity: 1,
        price: 500
      },
      {
        name: 'حزام جلدي',
        quantity: 1,
        price: 120
      }
    ],
    totalAmount: 620,
    deliveryStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];import { DeliveryCompany, Order } from './types';

export const deliveryCompanies: DeliveryCompany[] = [
  {
    id: 'msm-go',
    name: 'MSM GO',
    logo: '/images/MSM GO.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      },
      {
        name: 'المفتاح السري',
        key: 'secretKey',
        type: 'password',
        placeholder: 'أدخل المفتاح السري'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'noest-express',
    name: 'NOEST Express',
    logo: '/images/NOEST Express.jpeg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'اسم المستخدم',
        key: 'username',
        type: 'text',
        placeholder: 'أدخل اسم المستخدم'
      },
      {
        name: 'كلمة المرور',
        key: 'password',
        type: 'password',
        placeholder: 'أدخل كلمة المرور'
      },
      {
        name: 'رقم الحساب',
        key: 'accountId',
        type: 'text',
        placeholder: 'أدخل رقم الحساب'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'rocket-delivery',
    name: 'ROCKET DELIVERY',
    logo: '/images/ROCKET DELIVERY.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: false
    }
  },
  {
    id: 'world-express',
    name: 'WORLD EXPRESS',
    logo: '/images/WORLD EXPRESS.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      },
      {
        name: 'المفتاح السري',
        key: 'secretKey',
        type: 'password',
        placeholder: 'أدخل المفتاح السري'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'yalidine',
    name: 'Yalidine',
    logo: '/images/Yalidine.jpeg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'رمز التاجر',
        key: 'merchantId',
        type: 'text',
        placeholder: 'أدخل رمز التاجر'
      },
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'zr-express',
    name: 'ZR Express',
    logo: '/images/ZR Express.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'اسم المستخدم',
        key: 'username',
        type: 'text',
        placeholder: 'أدخل اسم المستخدم'
      },
      {
        name: 'كلمة المرور',
        key: 'password',
        type: 'password',
        placeholder: 'أدخل كلمة المرور'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  }
];

// بيانات الطلبات النموذجية
export const sampleOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    customerName: 'أحمد محمد',
    customerPhone: '0501234567',
    customerAddress: 'شارع الملك فهد، حي العليا',
    city: 'الرياض',
    postalCode: '12345',
    products: [
      {
        name: 'قميص قطني',
        quantity: 2,
        price: 150
      },
      {
        name: 'بنطلون جينز',
        quantity: 1,
        price: 200
      }
    ],
    totalAmount: 500,
    deliveryStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    customerName: 'سارة عبدالله',
    customerPhone: '0567891234',
    customerAddress: 'شارع التحلية، حي السلامة',
    city: 'جدة',
    postalCode: '23456',
    products: [
      {
        name: 'فستان سهرة',
        quantity: 1,
        price: 350
      }
    ],
    totalAmount: 350,
    deliveryStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    orderNumber: 'ORD-003',
    customerName: 'خالد العتيبي',
    customerPhone: '0512345678',
    customerAddress: 'شارع الأمير سلطان، حي الخالدية',
    city: 'الدمام',
    postalCode: '34567',
    products: [
      {
        name: 'ساعة يد رجالية',
        quantity: 1,
        price: 500
      },
      {
        name: 'حزام جلدي',
        quantity: 1,
        price: 120
      }
    ],
    totalAmount: 620,
    deliveryStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];import { DeliveryCompany, Order } from './types';

export const deliveryCompanies: DeliveryCompany[] = [
  {
    id: 'msm-go',
    name: 'MSM GO',
    logo: '/images/MSM GO.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      },
      {
        name: 'المفتاح السري',
        key: 'secretKey',
        type: 'password',
        placeholder: 'أدخل المفتاح السري'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'noest-express',
    name: 'NOEST Express',
    logo: '/images/NOEST Express.jpeg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'اسم المستخدم',
        key: 'username',
        type: 'text',
        placeholder: 'أدخل اسم المستخدم'
      },
      {
        name: 'كلمة المرور',
        key: 'password',
        type: 'password',
        placeholder: 'أدخل كلمة المرور'
      },
      {
        name: 'رقم الحساب',
        key: 'accountId',
        type: 'text',
        placeholder: 'أدخل رقم الحساب'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'rocket-delivery',
    name: 'ROCKET DELIVERY',
    logo: '/images/ROCKET DELIVERY.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: false
    }
  },
  {
    id: 'world-express',
    name: 'WORLD EXPRESS',
    logo: '/images/WORLD EXPRESS.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      },
      {
        name: 'المفتاح السري',
        key: 'secretKey',
        type: 'password',
        placeholder: 'أدخل المفتاح السري'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'yalidine',
    name: 'Yalidine',
    logo: '/images/Yalidine.jpeg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'رمز التاجر',
        key: 'merchantId',
        type: 'text',
        placeholder: 'أدخل رمز التاجر'
      },
      {
        name: 'مفتاح API',
        key: 'apiKey',
        type: 'text',
        placeholder: 'أدخل مفتاح API الخاص بك'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  },
  {
    id: 'zr-express',
    name: 'ZR Express',
    logo: '/images/ZR Express.jpg',
    isActive: false,
    apiKeys: {},
    requiredKeys: [
      {
        name: 'اسم المستخدم',
        key: 'username',
        type: 'text',
        placeholder: 'أدخل اسم المستخدم'
      },
      {
        name: 'كلمة المرور',
        key: 'password',
        type: 'password',
        placeholder: 'أدخل كلمة المرور'
      }
    ],
    features: {
      getPrices: true,
      getTrackingNumber: true,
      getShippingLabel: true
    }
  }
];

// بيانات الطلبات النموذجية
export const sampleOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    customerName: 'أحمد محمد',
    customerPhone: '0501234567',
    customerAddress: 'شارع الملك فهد، حي العليا',
    city: 'الرياض',
    postalCode: '12345',
    products: [
      {
        name: 'قميص قطني',
        quantity: 2,
        price: 150
      },
      {
        name: 'بنطلون جينز',
        quantity: 1,
        price: 200
      }
    ],
    totalAmount: 500,
    deliveryStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    customerName: 'سارة عبدالله',
    customerPhone: '0567891234',
    customerAddress: 'شارع التحلية، حي السلامة',
    city: 'جدة',
    postalCode: '23456',
    products: [
      {
        name: 'فستان سهرة',
        quantity: 1,
        price: 350
      }
    ],
    totalAmount: 350,
    deliveryStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    orderNumber: 'ORD-003',
    customerName: 'خالد العتيبي',
    customerPhone: '0512345678',
    customerAddress: 'شارع الأمير سلطان، حي الخالدية',
    city: 'الدمام',
    postalCode: '34567',
    products: [
      {
        name: 'ساعة يد رجالية',
        quantity: 1,
        price: 500
      },
      {
        name: 'حزام جلدي',
        quantity: 1,
        price: 120
      }
    ],
    totalAmount: 620,
    deliveryStatus: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];