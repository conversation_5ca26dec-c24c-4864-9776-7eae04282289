'use client';

import { useState, useEffect } from 'react';
import DeliveryCompanyCard from '@/components/DeliveryCompanyCard';
import { deliveryCompanies as initialCompanies } from '@/lib/data';
import { DeliveryCompany } from '@/lib/types';

export default function DeliveryCompaniesPage() {
  const [companies, setCompanies] = useState<DeliveryCompany[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    // محاولة استرداد البيانات من التخزين المحلي
    const savedCompanies = localStorage.getItem('deliveryCompanies');
    if (savedCompanies) {
      setCompanies(JSON.parse(savedCompanies));
    } else {
      setCompanies(initialCompanies);
    }
  }, []);

  // حفظ البيانات في التخزين المحلي عند تغييرها
  useEffect(() => {
    if (companies.length > 0) {
      localStorage.setItem('deliveryCompanies', JSON.stringify(companies));
    }
  }, [companies]);

  const handleToggleActive = (id: string, isActive: boolean) => {
    setCompanies(prevCompanies => 
      prevCompanies.map(company => 
        company.id === id ? { ...company, isActive } : company
      )
    );
  };

  const handleSaveApiKeys = (id: string, apiKeys: Record<string, string>) => {
    setCompanies(prevCompanies => 
      prevCompanies.map(company => 
        company.id === id ? { ...company, apiKeys } : company
      )
    );
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">شركات التوصيل</h1>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={() => setViewMode('grid')}
            className={`btn ${viewMode === 'grid' ? 'btn-primary' : 'btn-secondary'}`}
          >
            عرض شبكي
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`btn ${viewMode === 'list' ? 'btn-primary' : 'btn-secondary'}`}
          >
            عرض قائمة
          </button>
        </div>
      </div>

      <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-6' : 'space-y-6'}>
        {companies.map(company => (
          <DeliveryCompanyCard
            key={company.id}
            company={company}
            onToggleActive={handleToggleActive}
            onSaveApiKeys={handleSaveApiKeys}
          />
        ))}
      </div>
    </div>
  );
}'use client';

import { useState, useEffect } from 'react';
import DeliveryCompanyCard from '@/components/DeliveryCompanyCard';
import { deliveryCompanies as initialCompanies } from '@/lib/data';
import { DeliveryCompany } from '@/lib/types';

export default function DeliveryCompaniesPage() {
  const [companies, setCompanies] = useState<DeliveryCompany[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    // محاولة استرداد البيانات من التخزين المحلي
    const savedCompanies = localStorage.getItem('deliveryCompanies');
    if (savedCompanies) {
      setCompanies(JSON.parse(savedCompanies));
    } else {
      setCompanies(initialCompanies);
    }
  }, []);

  // حفظ البيانات في التخزين المحلي عند تغييرها
  useEffect(() => {
    if (companies.length > 0) {
      localStorage.setItem('deliveryCompanies', JSON.stringify(companies));
    }
  }, [companies]);

  const handleToggleActive = (id: string, isActive: boolean) => {
    setCompanies(prevCompanies => 
      prevCompanies.map(company => 
        company.id === id ? { ...company, isActive } : company
      )
    );
  };

  const handleSaveApiKeys = (id: string, apiKeys: Record<string, string>) => {
    setCompanies(prevCompanies => 
      prevCompanies.map(company => 
        company.id === id ? { ...company, apiKeys } : company
      )
    );
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">شركات التوصيل</h1>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={() => setViewMode('grid')}
            className={`btn ${viewMode === 'grid' ? 'btn-primary' : 'btn-secondary'}`}
          >
            عرض شبكي
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`btn ${viewMode === 'list' ? 'btn-primary' : 'btn-secondary'}`}
          >
            عرض قائمة
          </button>
        </div>
      </div>

      <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-6' : 'space-y-6'}>
        {companies.map(company => (
          <DeliveryCompanyCard
            key={company.id}
            company={company}
            onToggleActive={handleToggleActive}
            onSaveApiKeys={handleSaveApiKeys}
          />
        ))}
      </div>
    </div>
  );
}'use client';

import { useState, useEffect } from 'react';
import DeliveryCompanyCard from '@/components/DeliveryCompanyCard';
import { deliveryCompanies as initialCompanies } from '@/lib/data';
import { DeliveryCompany } from '@/lib/types';

export default function DeliveryCompaniesPage() {
  const [companies, setCompanies] = useState<DeliveryCompany[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    // محاولة استرداد البيانات من التخزين المحلي
    const savedCompanies = localStorage.getItem('deliveryCompanies');
    if (savedCompanies) {
      setCompanies(JSON.parse(savedCompanies));
    } else {
      setCompanies(initialCompanies);
    }
  }, []);

  // حفظ البيانات في التخزين المحلي عند تغييرها
  useEffect(() => {
    if (companies.length > 0) {
      localStorage.setItem('deliveryCompanies', JSON.stringify(companies));
    }
  }, [companies]);

  const handleToggleActive = (id: string, isActive: boolean) => {
    setCompanies(prevCompanies => 
      prevCompanies.map(company => 
        company.id === id ? { ...company, isActive } : company
      )
    );
  };

  const handleSaveApiKeys = (id: string, apiKeys: Record<string, string>) => {
    setCompanies(prevCompanies => 
      prevCompanies.map(company => 
        company.id === id ? { ...company, apiKeys } : company
      )
    );
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">شركات التوصيل</h1>
        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={() => setViewMode('grid')}
            className={`btn ${viewMode === 'grid' ? 'btn-primary' : 'btn-secondary'}`}
          >
            عرض شبكي
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`btn ${viewMode === 'list' ? 'btn-primary' : 'btn-secondary'}`}
          >
            عرض قائمة
          </button>
        </div>
      </div>

      <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-6' : 'space-y-6'}>
        {companies.map(company => (
          <DeliveryCompanyCard
            key={company.id}
            company={company}
            onToggleActive={handleToggleActive}
            onSaveApiKeys={handleSaveApiKeys}
          />
        ))}
      </div>
    </div>
  );
}