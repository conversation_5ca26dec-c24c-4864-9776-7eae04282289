import Link from 'next/link'

export default function Home() {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="card hover:shadow-lg transition-shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
          <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
          <Link href="/delivery-companies" className="btn btn-primary block text-center">
            إدارة شركات التوصيل
          </Link>
        </div>

        <div className="card hover:shadow-lg transition-shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
          <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
          <Link href="/orders" className="btn btn-primary block text-center">
            إدارة الطلبات
          </Link>
        </div>

        <div className="card hover:shadow-lg transition-shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
          <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
          <Link href="/shipping-labels" className="btn btn-primary block text-center">
            طباعة الملصقات
          </Link>
        </div>
      </div>

      <div className="card">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {[
            'MSM GO', 
            'NOEST Express', 
            'ROCKET DELIVERY', 
            'WORLD EXPRESS', 
            'Yalidine', 
            'ZR Express'
          ].map((company) => (
            <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
              <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
                {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
                <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
              </div>
              <span className="text-sm font-medium text-center">{company}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}import Link from 'next/link'

export default function Home() {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="card hover:shadow-lg transition-shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
          <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
          <Link href="/delivery-companies" className="btn btn-primary block text-center">
            إدارة شركات التوصيل
          </Link>
        </div>

        <div className="card hover:shadow-lg transition-shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
          <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
          <Link href="/orders" className="btn btn-primary block text-center">
            إدارة الطلبات
          </Link>
        </div>

        <div className="card hover:shadow-lg transition-shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
          <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
          <Link href="/shipping-labels" className="btn btn-primary block text-center">
            طباعة الملصقات
          </Link>
        </div>
      </div>

      <div className="card">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {[
            'MSM GO', 
            'NOEST Express', 
            'ROCKET DELIVERY', 
            'WORLD EXPRESS', 
            'Yalidine', 
            'ZR Express'
          ].map((company) => (
            <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
              <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
                {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
                <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
              </div>
              <span className="text-sm font-medium text-center">{company}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}import Link from 'next/link'

export default function Home() {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="card hover:shadow-lg transition-shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
          <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
          <Link href="/delivery-companies" className="btn btn-primary block text-center">
            إدارة شركات التوصيل
          </Link>
        </div>

        <div className="card hover:shadow-lg transition-shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
          <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
          <Link href="/orders" className="btn btn-primary block text-center">
            إدارة الطلبات
          </Link>
        </div>

        <div className="card hover:shadow-lg transition-shadow">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
          <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
          <Link href="/shipping-labels" className="btn btn-primary block text-center">
            طباعة الملصقات
          </Link>
        </div>
      </div>

      <div className="card">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {[
            'MSM GO', 
            'NOEST Express', 
            'ROCKET DELIVERY', 
            'WORLD EXPRESS', 
            'Yalidine', 
            'ZR Express'
          ].map((company) => (
            <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
              <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
                {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
                <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
              </div>
              <span className="text-sm font-medium text-center">{company}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}