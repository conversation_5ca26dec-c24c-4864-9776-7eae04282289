'use client';

import { useState, useEffect } from 'react';
import OrderCard from '@/components/OrderCard';
import { sampleOrders } from '@/lib/data';
import { Order, DeliveryCompany } from '@/lib/types';

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [deliveryCompanies, setDeliveryCompanies] = useState<DeliveryCompany[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاولة استرداد البيانات من التخزين المحلي
    const savedOrders = localStorage.getItem('orders');
    const savedCompanies = localStorage.getItem('deliveryCompanies');
    
    if (savedOrders) {
      setOrders(JSON.parse(savedOrders));
    } else {
      setOrders(sampleOrders);
      localStorage.setItem('orders', JSON.stringify(sampleOrders));
    }
    
    if (savedCompanies) {
      setDeliveryCompanies(JSON.parse(savedCompanies));
    }
    
    setIsLoading(false);
  }, []);

  // محاكاة مزامنة الطلب مع شركة التوصيل
  const handleSyncOrder = (orderId: string, deliveryCompanyId: string) => {
    // في التطبيق الحقيقي، هنا ستقوم بإرسال طلب API إلى شركة التوصيل
    // للحصول على سعر التوصيل ورقم التتبع
    
    // محاكاة استجابة API
    setTimeout(() => {
      const company = deliveryCompanies.find(c => c.id === deliveryCompanyId);
      
      if (!company) return;
      
      const trackingNumber = `TRK-${Math.floor(Math.random() * 1000000)}`;
      const shippingCost = Math.floor(Math.random() * 50) + 10; // 10-60 ريال
      
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId 
            ? { 
                ...order, 
                deliveryCompany: deliveryCompanyId,
                deliveryStatus: 'processing',
                trackingNumber,
                shippingCost,
                updatedAt: new Date().toISOString()
              } 
            : order
        )
      );
      
      // حفظ التغييرات في التخزين المحلي
      const updatedOrders = orders.map(order => 
        order.id === orderId 
          ? { 
              ...order, 
              deliveryCompany: deliveryCompanyId,
              deliveryStatus: 'processing',
              trackingNumber,
              shippingCost,
              updatedAt: new Date().toISOString()
            } 
          : order
      );
      
      localStorage.setItem('orders', JSON.stringify(updatedOrders));
      
      alert(`تمت مزامنة الطلب بنجاح مع ${company.name}!\nرقم التتبع: ${trackingNumber}\nتكلفة الشحن: ${shippingCost} ريال`);
    }, 1500);
  };

  // محاكاة طباعة ملصق الشحن
  const handlePrintLabel = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order || !order.deliveryCompany || !order.trackingNumber) return;
    
    const company = deliveryCompanies.find(c => c.id === order.deliveryCompany);
    if (!company) return;
    
    alert(`جاري تحميل ملصق الشحن للطلب ${order.orderNumber} من ${company.name}...\nرقم التتبع: ${order.trackingNumber}`);
    
    // في التطبيق الحقيقي، هنا ستقوم بتحميل ملصق الشحن من شركة التوصيل
    // وعرضه للطباعة
  };

  if (isLoading) {
    return <div className="text-center py-10">جاري التحميل...</div>;
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">إدارة الطلبات</h1>
      
      <div className="space-y-6">
        {orders.length === 0 ? (
          <div className="text-center py-10 bg-gray-50 rounded-lg">
            <p className="text-gray-500">لا توجد طلبات حالياً</p>
          </div>
        ) : (
          orders.map(order => (
            <OrderCard
              key={order.id}
              order={order}
              deliveryCompanies={deliveryCompanies}
              onSyncOrder={handleSyncOrder}
              onPrintLabel={handlePrintLabel}
            />
          ))
        )}
      </div>
    </div>
  );
}'use client';

import { useState, useEffect } from 'react';
import OrderCard from '@/components/OrderCard';
import { sampleOrders } from '@/lib/data';
import { Order, DeliveryCompany } from '@/lib/types';

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [deliveryCompanies, setDeliveryCompanies] = useState<DeliveryCompany[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاولة استرداد البيانات من التخزين المحلي
    const savedOrders = localStorage.getItem('orders');
    const savedCompanies = localStorage.getItem('deliveryCompanies');
    
    if (savedOrders) {
      setOrders(JSON.parse(savedOrders));
    } else {
      setOrders(sampleOrders);
      localStorage.setItem('orders', JSON.stringify(sampleOrders));
    }
    
    if (savedCompanies) {
      setDeliveryCompanies(JSON.parse(savedCompanies));
    }
    
    setIsLoading(false);
  }, []);

  // محاكاة مزامنة الطلب مع شركة التوصيل
  const handleSyncOrder = (orderId: string, deliveryCompanyId: string) => {
    // في التطبيق الحقيقي، هنا ستقوم بإرسال طلب API إلى شركة التوصيل
    // للحصول على سعر التوصيل ورقم التتبع
    
    // محاكاة استجابة API
    setTimeout(() => {
      const company = deliveryCompanies.find(c => c.id === deliveryCompanyId);
      
      if (!company) return;
      
      const trackingNumber = `TRK-${Math.floor(Math.random() * 1000000)}`;
      const shippingCost = Math.floor(Math.random() * 50) + 10; // 10-60 ريال
      
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId 
            ? { 
                ...order, 
                deliveryCompany: deliveryCompanyId,
                deliveryStatus: 'processing',
                trackingNumber,
                shippingCost,
                updatedAt: new Date().toISOString()
              } 
            : order
        )
      );
      
      // حفظ التغييرات في التخزين المحلي
      const updatedOrders = orders.map(order => 
        order.id === orderId 
          ? { 
              ...order, 
              deliveryCompany: deliveryCompanyId,
              deliveryStatus: 'processing',
              trackingNumber,
              shippingCost,
              updatedAt: new Date().toISOString()
            } 
          : order
      );
      
      localStorage.setItem('orders', JSON.stringify(updatedOrders));
      
      alert(`تمت مزامنة الطلب بنجاح مع ${company.name}!\nرقم التتبع: ${trackingNumber}\nتكلفة الشحن: ${shippingCost} ريال`);
    }, 1500);
  };

  // محاكاة طباعة ملصق الشحن
  const handlePrintLabel = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order || !order.deliveryCompany || !order.trackingNumber) return;
    
    const company = deliveryCompanies.find(c => c.id === order.deliveryCompany);
    if (!company) return;
    
    alert(`جاري تحميل ملصق الشحن للطلب ${order.orderNumber} من ${company.name}...\nرقم التتبع: ${order.trackingNumber}`);
    
    // في التطبيق الحقيقي، هنا ستقوم بتحميل ملصق الشحن من شركة التوصيل
    // وعرضه للطباعة
  };

  if (isLoading) {
    return <div className="text-center py-10">جاري التحميل...</div>;
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">إدارة الطلبات</h1>
      
      <div className="space-y-6">
        {orders.length === 0 ? (
          <div className="text-center py-10 bg-gray-50 rounded-lg">
            <p className="text-gray-500">لا توجد طلبات حالياً</p>
          </div>
        ) : (
          orders.map(order => (
            <OrderCard
              key={order.id}
              order={order}
              deliveryCompanies={deliveryCompanies}
              onSyncOrder={handleSyncOrder}
              onPrintLabel={handlePrintLabel}
            />
          ))
        )}
      </div>
    </div>
  );
}'use client';

import { useState, useEffect } from 'react';
import OrderCard from '@/components/OrderCard';
import { sampleOrders } from '@/lib/data';
import { Order, DeliveryCompany } from '@/lib/types';

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [deliveryCompanies, setDeliveryCompanies] = useState<DeliveryCompany[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاولة استرداد البيانات من التخزين المحلي
    const savedOrders = localStorage.getItem('orders');
    const savedCompanies = localStorage.getItem('deliveryCompanies');
    
    if (savedOrders) {
      setOrders(JSON.parse(savedOrders));
    } else {
      setOrders(sampleOrders);
      localStorage.setItem('orders', JSON.stringify(sampleOrders));
    }
    
    if (savedCompanies) {
      setDeliveryCompanies(JSON.parse(savedCompanies));
    }
    
    setIsLoading(false);
  }, []);

  // محاكاة مزامنة الطلب مع شركة التوصيل
  const handleSyncOrder = (orderId: string, deliveryCompanyId: string) => {
    // في التطبيق الحقيقي، هنا ستقوم بإرسال طلب API إلى شركة التوصيل
    // للحصول على سعر التوصيل ورقم التتبع
    
    // محاكاة استجابة API
    setTimeout(() => {
      const company = deliveryCompanies.find(c => c.id === deliveryCompanyId);
      
      if (!company) return;
      
      const trackingNumber = `TRK-${Math.floor(Math.random() * 1000000)}`;
      const shippingCost = Math.floor(Math.random() * 50) + 10; // 10-60 ريال
      
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId 
            ? { 
                ...order, 
                deliveryCompany: deliveryCompanyId,
                deliveryStatus: 'processing',
                trackingNumber,
                shippingCost,
                updatedAt: new Date().toISOString()
              } 
            : order
        )
      );
      
      // حفظ التغييرات في التخزين المحلي
      const updatedOrders = orders.map(order => 
        order.id === orderId 
          ? { 
              ...order, 
              deliveryCompany: deliveryCompanyId,
              deliveryStatus: 'processing',
              trackingNumber,
              shippingCost,
              updatedAt: new Date().toISOString()
            } 
          : order
      );
      
      localStorage.setItem('orders', JSON.stringify(updatedOrders));
      
      alert(`تمت مزامنة الطلب بنجاح مع ${company.name}!\nرقم التتبع: ${trackingNumber}\nتكلفة الشحن: ${shippingCost} ريال`);
    }, 1500);
  };

  // محاكاة طباعة ملصق الشحن
  const handlePrintLabel = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order || !order.deliveryCompany || !order.trackingNumber) return;
    
    const company = deliveryCompanies.find(c => c.id === order.deliveryCompany);
    if (!company) return;
    
    alert(`جاري تحميل ملصق الشحن للطلب ${order.orderNumber} من ${company.name}...\nرقم التتبع: ${order.trackingNumber}`);
    
    // في التطبيق الحقيقي، هنا ستقوم بتحميل ملصق الشحن من شركة التوصيل
    // وعرضه للطباعة
  };

  if (isLoading) {
    return <div className="text-center py-10">جاري التحميل...</div>;
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">إدارة الطلبات</h1>
      
      <div className="space-y-6">
        {orders.length === 0 ? (
          <div className="text-center py-10 bg-gray-50 rounded-lg">
            <p className="text-gray-500">لا توجد طلبات حالياً</p>
          </div>
        ) : (
          orders.map(order => (
            <OrderCard
              key={order.id}
              order={order}
              deliveryCompanies={deliveryCompanies}
              onSyncOrder={handleSyncOrder}
              onPrintLabel={handlePrintLabel}
            />
          ))
        )}
      </div>
    </div>
  );
}