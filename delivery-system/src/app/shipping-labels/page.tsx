'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Order, DeliveryCompany } from '@/lib/types';

export default function ShippingLabelsPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [companies, setCompanies] = useState<DeliveryCompany[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاولة استرداد البيانات من التخزين المحلي
    const savedOrders = localStorage.getItem('orders');
    const savedCompanies = localStorage.getItem('deliveryCompanies');
    
    if (savedOrders) {
      setOrders(JSON.parse(savedOrders));
    }
    
    if (savedCompanies) {
      setCompanies(JSON.parse(savedCompanies));
    }
    
    setIsLoading(false);
  }, []);

  // الحصول على الطلبات التي تم مزامنتها مع شركات التوصيل
  const syncedOrders = orders.filter(order => 
    order.deliveryCompany && order.trackingNumber
  );

  const getCompanyName = (companyId: string) => {
    const company = companies.find(c => c.id === companyId);
    return company ? company.name : companyId;
  };

  const getCompanyLogo = (companyId: string) => {
    const company = companies.find(c => c.id === companyId);
    return company ? company.logo : '';
  };

  const handlePrintLabel = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order || !order.deliveryCompany || !order.trackingNumber) return;
    
    const company = companies.find(c => c.id === order.deliveryCompany);
    if (!company) return;
    
    alert(`جاري تحميل ملصق الشحن للطلب ${order.orderNumber} من ${company.name}...\nرقم التتبع: ${order.trackingNumber}`);
    
    // في التطبيق الحقيقي، هنا ستقوم بتحميل ملصق الشحن من شركة التوصيل
    // وعرضه للطباعة
  };

  const handlePrintAllLabels = () => {
    if (syncedOrders.length === 0) return;
    
    alert(`جاري تحميل ${syncedOrders.length} ملصق شحن للطباعة...`);
    
    // في التطبيق الحقيقي، هنا ستقوم بتحميل جميع ملصقات الشحن
    // وعرضها للطباعة
  };

  if (isLoading) {
    return <div className="text-center py-10">جاري التحميل...</div>;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">ملصقات الشحن</h1>
        {syncedOrders.length > 0 && (
          <button
            onClick={handlePrintAllLabels}
            className="btn btn-primary"
          >
            طباعة جميع الملصقات ({syncedOrders.length})
          </button>
        )}
      </div>
      
      {syncedOrders.length === 0 ? (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          <p className="text-gray-500">لا توجد ملصقات شحن متاحة</p>
          <p className="text-gray-500 mt-2">قم بمزامنة الطلبات مع شركات التوصيل أولاً</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {syncedOrders.map(order => (
            <div key={order.id} className="card hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">طلب #{order.orderNumber}</h3>
                <span className="badge badge-success">
                  {order.deliveryStatus === 'shipped' ? 'تم الشحن' : 'قيد المعالجة'}
                </span>
              </div>
              
              <div className="flex items-center mb-4">
                {order.deliveryCompany && (
                  <div className="relative w-12 h-12 mr-3 overflow-hidden rounded-lg">
                    <Image
                      src={getCompanyLogo(order.deliveryCompany)}
                      alt={getCompanyName(order.deliveryCompany)}
                      fill
                      style={{ objectFit: 'contain' }}
                    />
                  </div>
                )}
                <div>
                  <div className="text-sm font-medium">شركة التوصيل</div>
                  <div className="text-sm">{order.deliveryCompany ? getCompanyName(order.deliveryCompany) : 'غير محدد'}</div>
                </div>
              </div>
              
              <div className="mb-4">
                <div className="text-sm font-medium">رقم التتبع</div>
                <div className="text-sm font-mono bg-gray-50 p-2 rounded mt-1">
                  {order.trackingNumber}
                </div>
              </div>
              
              <div className="mb-4">
                <div className="text-sm font-medium">معلومات العميل</div>
                <div className="text-sm mt-1">{order.customerName}</div>
                <div className="text-sm">{order.customerPhone}</div>
                <div className="text-sm">{order.customerAddress}, {order.city}</div>
              </div>
              
              <button
                onClick={() => handlePrintLabel(order.id)}
                className="btn btn-primary w-full"
              >
                طباعة الملصق
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Order, DeliveryCompany } from '@/lib/types';

export default function ShippingLabelsPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [companies, setCompanies] = useState<DeliveryCompany[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاولة استرداد البيانات من التخزين المحلي
    const savedOrders = localStorage.getItem('orders');
    const savedCompanies = localStorage.getItem('deliveryCompanies');
    
    if (savedOrders) {
      setOrders(JSON.parse(savedOrders));
    }
    
    if (savedCompanies) {
      setCompanies(JSON.parse(savedCompanies));
    }
    
    setIsLoading(false);
  }, []);

  // الحصول على الطلبات التي تم مزامنتها مع شركات التوصيل
  const syncedOrders = orders.filter(order => 
    order.deliveryCompany && order.trackingNumber
  );

  const getCompanyName = (companyId: string) => {
    const company = companies.find(c => c.id === companyId);
    return company ? company.name : companyId;
  };

  const getCompanyLogo = (companyId: string) => {
    const company = companies.find(c => c.id === companyId);
    return company ? company.logo : '';
  };

  const handlePrintLabel = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order || !order.deliveryCompany || !order.trackingNumber) return;
    
    const company = companies.find(c => c.id === order.deliveryCompany);
    if (!company) return;
    
    alert(`جاري تحميل ملصق الشحن للطلب ${order.orderNumber} من ${company.name}...\nرقم التتبع: ${order.trackingNumber}`);
    
    // في التطبيق الحقيقي، هنا ستقوم بتحميل ملصق الشحن من شركة التوصيل
    // وعرضه للطباعة
  };

  const handlePrintAllLabels = () => {
    if (syncedOrders.length === 0) return;
    
    alert(`جاري تحميل ${syncedOrders.length} ملصق شحن للطباعة...`);
    
    // في التطبيق الحقيقي، هنا ستقوم بتحميل جميع ملصقات الشحن
    // وعرضها للطباعة
  };

  if (isLoading) {
    return <div className="text-center py-10">جاري التحميل...</div>;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">ملصقات الشحن</h1>
        {syncedOrders.length > 0 && (
          <button
            onClick={handlePrintAllLabels}
            className="btn btn-primary"
          >
            طباعة جميع الملصقات ({syncedOrders.length})
          </button>
        )}
      </div>
      
      {syncedOrders.length === 0 ? (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          <p className="text-gray-500">لا توجد ملصقات شحن متاحة</p>
          <p className="text-gray-500 mt-2">قم بمزامنة الطلبات مع شركات التوصيل أولاً</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {syncedOrders.map(order => (
            <div key={order.id} className="card hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">طلب #{order.orderNumber}</h3>
                <span className="badge badge-success">
                  {order.deliveryStatus === 'shipped' ? 'تم الشحن' : 'قيد المعالجة'}
                </span>
              </div>
              
              <div className="flex items-center mb-4">
                {order.deliveryCompany && (
                  <div className="relative w-12 h-12 mr-3 overflow-hidden rounded-lg">
                    <Image
                      src={getCompanyLogo(order.deliveryCompany)}
                      alt={getCompanyName(order.deliveryCompany)}
                      fill
                      style={{ objectFit: 'contain' }}
                    />
                  </div>
                )}
                <div>
                  <div className="text-sm font-medium">شركة التوصيل</div>
                  <div className="text-sm">{order.deliveryCompany ? getCompanyName(order.deliveryCompany) : 'غير محدد'}</div>
                </div>
              </div>
              
              <div className="mb-4">
                <div className="text-sm font-medium">رقم التتبع</div>
                <div className="text-sm font-mono bg-gray-50 p-2 rounded mt-1">
                  {order.trackingNumber}
                </div>
              </div>
              
              <div className="mb-4">
                <div className="text-sm font-medium">معلومات العميل</div>
                <div className="text-sm mt-1">{order.customerName}</div>
                <div className="text-sm">{order.customerPhone}</div>
                <div className="text-sm">{order.customerAddress}, {order.city}</div>
              </div>
              
              <button
                onClick={() => handlePrintLabel(order.id)}
                className="btn btn-primary w-full"
              >
                طباعة الملصق
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Order, DeliveryCompany } from '@/lib/types';

export default function ShippingLabelsPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [companies, setCompanies] = useState<DeliveryCompany[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاولة استرداد البيانات من التخزين المحلي
    const savedOrders = localStorage.getItem('orders');
    const savedCompanies = localStorage.getItem('deliveryCompanies');
    
    if (savedOrders) {
      setOrders(JSON.parse(savedOrders));
    }
    
    if (savedCompanies) {
      setCompanies(JSON.parse(savedCompanies));
    }
    
    setIsLoading(false);
  }, []);

  // الحصول على الطلبات التي تم مزامنتها مع شركات التوصيل
  const syncedOrders = orders.filter(order => 
    order.deliveryCompany && order.trackingNumber
  );

  const getCompanyName = (companyId: string) => {
    const company = companies.find(c => c.id === companyId);
    return company ? company.name : companyId;
  };

  const getCompanyLogo = (companyId: string) => {
    const company = companies.find(c => c.id === companyId);
    return company ? company.logo : '';
  };

  const handlePrintLabel = (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order || !order.deliveryCompany || !order.trackingNumber) return;
    
    const company = companies.find(c => c.id === order.deliveryCompany);
    if (!company) return;
    
    alert(`جاري تحميل ملصق الشحن للطلب ${order.orderNumber} من ${company.name}...\nرقم التتبع: ${order.trackingNumber}`);
    
    // في التطبيق الحقيقي، هنا ستقوم بتحميل ملصق الشحن من شركة التوصيل
    // وعرضه للطباعة
  };

  const handlePrintAllLabels = () => {
    if (syncedOrders.length === 0) return;
    
    alert(`جاري تحميل ${syncedOrders.length} ملصق شحن للطباعة...`);
    
    // في التطبيق الحقيقي، هنا ستقوم بتحميل جميع ملصقات الشحن
    // وعرضها للطباعة
  };

  if (isLoading) {
    return <div className="text-center py-10">جاري التحميل...</div>;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">ملصقات الشحن</h1>
        {syncedOrders.length > 0 && (
          <button
            onClick={handlePrintAllLabels}
            className="btn btn-primary"
          >
            طباعة جميع الملصقات ({syncedOrders.length})
          </button>
        )}
      </div>
      
      {syncedOrders.length === 0 ? (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          <p className="text-gray-500">لا توجد ملصقات شحن متاحة</p>
          <p className="text-gray-500 mt-2">قم بمزامنة الطلبات مع شركات التوصيل أولاً</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {syncedOrders.map(order => (
            <div key={order.id} className="card hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">طلب #{order.orderNumber}</h3>
                <span className="badge badge-success">
                  {order.deliveryStatus === 'shipped' ? 'تم الشحن' : 'قيد المعالجة'}
                </span>
              </div>
              
              <div className="flex items-center mb-4">
                {order.deliveryCompany && (
                  <div className="relative w-12 h-12 mr-3 overflow-hidden rounded-lg">
                    <Image
                      src={getCompanyLogo(order.deliveryCompany)}
                      alt={getCompanyName(order.deliveryCompany)}
                      fill
                      style={{ objectFit: 'contain' }}
                    />
                  </div>
                )}
                <div>
                  <div className="text-sm font-medium">شركة التوصيل</div>
                  <div className="text-sm">{order.deliveryCompany ? getCompanyName(order.deliveryCompany) : 'غير محدد'}</div>
                </div>
              </div>
              
              <div className="mb-4">
                <div className="text-sm font-medium">رقم التتبع</div>
                <div className="text-sm font-mono bg-gray-50 p-2 rounded mt-1">
                  {order.trackingNumber}
                </div>
              </div>
              
              <div className="mb-4">
                <div className="text-sm font-medium">معلومات العميل</div>
                <div className="text-sm mt-1">{order.customerName}</div>
                <div className="text-sm">{order.customerPhone}</div>
                <div className="text-sm">{order.customerAddress}, {order.city}</div>
              </div>
              
              <button
                onClick={() => handlePrintLabel(order.id)}
                className="btn btn-primary w-full"
              >
                طباعة الملصق
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}