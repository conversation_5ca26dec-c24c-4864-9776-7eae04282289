import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'نظام مزامنة شركات التوصيل',
  description: 'نظام مزامنة طلبات المتجر مع شركات التوصيل',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={inter.className}>
        <div className="min-h-screen flex flex-col">
          <header className="bg-white shadow-sm">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between h-16 items-center">
                <div className="flex-shrink-0 flex items-center">
                  <h1 className="text-xl font-bold text-primary">نظام مزامنة شركات التوصيل</h1>
                </div>
                <nav className="flex space-x-4 space-x-reverse">
                  <a href="/" className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">الرئيسية</a>
                  <a href="/delivery-companies" className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">شركات التوصيل</a>
                  <a href="/orders" className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">الطلبات</a>
                </nav>
              </div>
            </div>
          </header>
          <main className="flex-grow">
            <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
              {children}
            </div>
          </main>
          <footer className="bg-white">
            <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
              <p className="text-center text-gray-500 text-sm">
                &copy; {new Date().getFullYear()} نظام مزامنة شركات التوصيل. جميع الحقوق محفوظة.
              </p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  )
}import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'نظام مزامنة شركات التوصيل',
  description: 'نظام مزامنة طلبات المتجر مع شركات التوصيل',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={inter.className}>
        <div className="min-h-screen flex flex-col">
          <header className="bg-white shadow-sm">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between h-16 items-center">
                <div className="flex-shrink-0 flex items-center">
                  <h1 className="text-xl font-bold text-primary">نظام مزامنة شركات التوصيل</h1>
                </div>
                <nav className="flex space-x-4 space-x-reverse">
                  <a href="/" className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">الرئيسية</a>
                  <a href="/delivery-companies" className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">شركات التوصيل</a>
                  <a href="/orders" className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">الطلبات</a>
                </nav>
              </div>
            </div>
          </header>
          <main className="flex-grow">
            <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
              {children}
            </div>
          </main>
          <footer className="bg-white">
            <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
              <p className="text-center text-gray-500 text-sm">
                &copy; {new Date().getFullYear()} نظام مزامنة شركات التوصيل. جميع الحقوق محفوظة.
              </p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  )
}import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'نظام مزامنة شركات التوصيل',
  description: 'نظام مزامنة طلبات المتجر مع شركات التوصيل',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={inter.className}>
        <div className="min-h-screen flex flex-col">
          <header className="bg-white shadow-sm">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between h-16 items-center">
                <div className="flex-shrink-0 flex items-center">
                  <h1 className="text-xl font-bold text-primary">نظام مزامنة شركات التوصيل</h1>
                </div>
                <nav className="flex space-x-4 space-x-reverse">
                  <a href="/" className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">الرئيسية</a>
                  <a href="/delivery-companies" className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">شركات التوصيل</a>
                  <a href="/orders" className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">الطلبات</a>
                </nav>
              </div>
            </div>
          </header>
          <main className="flex-grow">
            <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
              {children}
            </div>
          </main>
          <footer className="bg-white">
            <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
              <p className="text-center text-gray-500 text-sm">
                &copy; {new Date().getFullYear()} نظام مزامنة شركات التوصيل. جميع الحقوق محفوظة.
              </p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  )
}