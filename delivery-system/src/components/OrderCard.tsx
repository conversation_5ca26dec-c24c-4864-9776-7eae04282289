'use client';

import { useState } from 'react';
import { Order, DeliveryCompany } from '@/lib/types';

interface OrderCardProps {
  order: Order;
  deliveryCompanies: DeliveryCompany[];
  onSyncOrder: (orderId: string, deliveryCompanyId: string) => void;
  onPrintLabel: (orderId: string) => void;
}

export default function OrderCard({ 
  order, 
  deliveryCompanies,
  onSyncOrder,
  onPrintLabel
}: OrderCardProps) {
  const [selectedCompany, setSelectedCompany] = useState(order.deliveryCompany || '');
  
  const activeCompanies = deliveryCompanies.filter(company => company.isActive);
  
  const handleSyncOrder = () => {
    if (selectedCompany) {
      onSyncOrder(order.id, selectedCompany);
    }
  };

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    shipped: 'bg-green-100 text-green-800',
    delivered: 'bg-gray-100 text-gray-800'
  };

  const statusText = {
    pending: 'قيد الانتظار',
    processing: 'قيد المعالجة',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم'
  };

  return (
    <div className="card">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold">طلب #{order.orderNumber}</h3>
          <p className="text-sm text-gray-500">
            {new Date(order.createdAt).toLocaleDateString('ar-SA')}
          </p>
        </div>
        <div className={`px-3 py-1 rounded-full text-sm ${order.deliveryStatus ? statusColors[order.deliveryStatus] : 'bg-gray-100'}`}>
          {order.deliveryStatus ? statusText[order.deliveryStatus] : 'غير محدد'}
        </div>
      </div>

      <div className="mb-4">
        <h4 className="text-md font-medium mb-2">معلومات العميل</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
          <div>
            <span className="font-medium">الاسم:</span> {order.customerName}
          </div>
          <div>
            <span className="font-medium">الهاتف:</span> {order.customerPhone}
          </div>
          <div className="md:col-span-2">
            <span className="font-medium">العنوان:</span> {order.customerAddress}, {order.city}
            {order.postalCode && `, ${order.postalCode}`}
          </div>
        </div>
      </div>

      <div className="mb-4">
        <h4 className="text-md font-medium mb-2">المنتجات</h4>
        <div className="space-y-2">
          {order.products.map((product, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span>{product.name} (x{product.quantity})</span>
              <span>{product.price * product.quantity} ريال</span>
            </div>
          ))}
          <div className="border-t pt-2 flex justify-between font-medium">
            <span>المجموع</span>
            <span>{order.totalAmount} ريال</span>
          </div>
          {order.shippingCost && (
            <div className="flex justify-between text-sm">
              <span>تكلفة الشحن</span>
              <span>{order.shippingCost} ريال</span>
            </div>
          )}
        </div>
      </div>

      {order.trackingNumber && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-sm font-medium">رقم التتبع:</span>
              <span className="text-sm ml-2">{order.trackingNumber}</span>
            </div>
            <button
              onClick={() => onPrintLabel(order.id)}
              className="btn btn-secondary text-sm py-1"
              disabled={!order.trackingNumber}
            >
              طباعة الملصق
            </button>
          </div>
        </div>
      )}

      <div className="mt-4 border-t pt-4">
        <div className="flex flex-col md:flex-row gap-2">
          <select
            value={selectedCompany}
            onChange={(e) => setSelectedCompany(e.target.value)}
            className="input md:flex-1"
            disabled={!!order.deliveryCompany || activeCompanies.length === 0}
          >
            <option value="">اختر شركة التوصيل</option>
            {activeCompanies.map(company => (
              <option key={company.id} value={company.id}>
                {company.name}
              </option>
            ))}
          </select>
          <button
            onClick={handleSyncOrder}
            className="btn btn-primary"
            disabled={!selectedCompany || !!order.deliveryCompany}
          >
            مزامنة الطلب
          </button>
        </div>
      </div>
    </div>
  );
}'use client';

import { useState } from 'react';
import { Order, DeliveryCompany } from '@/lib/types';

interface OrderCardProps {
  order: Order;
  deliveryCompanies: DeliveryCompany[];
  onSyncOrder: (orderId: string, deliveryCompanyId: string) => void;
  onPrintLabel: (orderId: string) => void;
}

export default function OrderCard({ 
  order, 
  deliveryCompanies,
  onSyncOrder,
  onPrintLabel
}: OrderCardProps) {
  const [selectedCompany, setSelectedCompany] = useState(order.deliveryCompany || '');
  
  const activeCompanies = deliveryCompanies.filter(company => company.isActive);
  
  const handleSyncOrder = () => {
    if (selectedCompany) {
      onSyncOrder(order.id, selectedCompany);
    }
  };

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    shipped: 'bg-green-100 text-green-800',
    delivered: 'bg-gray-100 text-gray-800'
  };

  const statusText = {
    pending: 'قيد الانتظار',
    processing: 'قيد المعالجة',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم'
  };

  return (
    <div className="card">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold">طلب #{order.orderNumber}</h3>
          <p className="text-sm text-gray-500">
            {new Date(order.createdAt).toLocaleDateString('ar-SA')}
          </p>
        </div>
        <div className={`px-3 py-1 rounded-full text-sm ${order.deliveryStatus ? statusColors[order.deliveryStatus] : 'bg-gray-100'}`}>
          {order.deliveryStatus ? statusText[order.deliveryStatus] : 'غير محدد'}
        </div>
      </div>

      <div className="mb-4">
        <h4 className="text-md font-medium mb-2">معلومات العميل</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
          <div>
            <span className="font-medium">الاسم:</span> {order.customerName}
          </div>
          <div>
            <span className="font-medium">الهاتف:</span> {order.customerPhone}
          </div>
          <div className="md:col-span-2">
            <span className="font-medium">العنوان:</span> {order.customerAddress}, {order.city}
            {order.postalCode && `, ${order.postalCode}`}
          </div>
        </div>
      </div>

      <div className="mb-4">
        <h4 className="text-md font-medium mb-2">المنتجات</h4>
        <div className="space-y-2">
          {order.products.map((product, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span>{product.name} (x{product.quantity})</span>
              <span>{product.price * product.quantity} ريال</span>
            </div>
          ))}
          <div className="border-t pt-2 flex justify-between font-medium">
            <span>المجموع</span>
            <span>{order.totalAmount} ريال</span>
          </div>
          {order.shippingCost && (
            <div className="flex justify-between text-sm">
              <span>تكلفة الشحن</span>
              <span>{order.shippingCost} ريال</span>
            </div>
          )}
        </div>
      </div>

      {order.trackingNumber && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-sm font-medium">رقم التتبع:</span>
              <span className="text-sm ml-2">{order.trackingNumber}</span>
            </div>
            <button
              onClick={() => onPrintLabel(order.id)}
              className="btn btn-secondary text-sm py-1"
              disabled={!order.trackingNumber}
            >
              طباعة الملصق
            </button>
          </div>
        </div>
      )}

      <div className="mt-4 border-t pt-4">
        <div className="flex flex-col md:flex-row gap-2">
          <select
            value={selectedCompany}
            onChange={(e) => setSelectedCompany(e.target.value)}
            className="input md:flex-1"
            disabled={!!order.deliveryCompany || activeCompanies.length === 0}
          >
            <option value="">اختر شركة التوصيل</option>
            {activeCompanies.map(company => (
              <option key={company.id} value={company.id}>
                {company.name}
              </option>
            ))}
          </select>
          <button
            onClick={handleSyncOrder}
            className="btn btn-primary"
            disabled={!selectedCompany || !!order.deliveryCompany}
          >
            مزامنة الطلب
          </button>
        </div>
      </div>
    </div>
  );
}'use client';

import { useState } from 'react';
import { Order, DeliveryCompany } from '@/lib/types';

interface OrderCardProps {
  order: Order;
  deliveryCompanies: DeliveryCompany[];
  onSyncOrder: (orderId: string, deliveryCompanyId: string) => void;
  onPrintLabel: (orderId: string) => void;
}

export default function OrderCard({ 
  order, 
  deliveryCompanies,
  onSyncOrder,
  onPrintLabel
}: OrderCardProps) {
  const [selectedCompany, setSelectedCompany] = useState(order.deliveryCompany || '');
  
  const activeCompanies = deliveryCompanies.filter(company => company.isActive);
  
  const handleSyncOrder = () => {
    if (selectedCompany) {
      onSyncOrder(order.id, selectedCompany);
    }
  };

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    shipped: 'bg-green-100 text-green-800',
    delivered: 'bg-gray-100 text-gray-800'
  };

  const statusText = {
    pending: 'قيد الانتظار',
    processing: 'قيد المعالجة',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم'
  };

  return (
    <div className="card">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold">طلب #{order.orderNumber}</h3>
          <p className="text-sm text-gray-500">
            {new Date(order.createdAt).toLocaleDateString('ar-SA')}
          </p>
        </div>
        <div className={`px-3 py-1 rounded-full text-sm ${order.deliveryStatus ? statusColors[order.deliveryStatus] : 'bg-gray-100'}`}>
          {order.deliveryStatus ? statusText[order.deliveryStatus] : 'غير محدد'}
        </div>
      </div>

      <div className="mb-4">
        <h4 className="text-md font-medium mb-2">معلومات العميل</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
          <div>
            <span className="font-medium">الاسم:</span> {order.customerName}
          </div>
          <div>
            <span className="font-medium">الهاتف:</span> {order.customerPhone}
          </div>
          <div className="md:col-span-2">
            <span className="font-medium">العنوان:</span> {order.customerAddress}, {order.city}
            {order.postalCode && `, ${order.postalCode}`}
          </div>
        </div>
      </div>

      <div className="mb-4">
        <h4 className="text-md font-medium mb-2">المنتجات</h4>
        <div className="space-y-2">
          {order.products.map((product, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span>{product.name} (x{product.quantity})</span>
              <span>{product.price * product.quantity} ريال</span>
            </div>
          ))}
          <div className="border-t pt-2 flex justify-between font-medium">
            <span>المجموع</span>
            <span>{order.totalAmount} ريال</span>
          </div>
          {order.shippingCost && (
            <div className="flex justify-between text-sm">
              <span>تكلفة الشحن</span>
              <span>{order.shippingCost} ريال</span>
            </div>
          )}
        </div>
      </div>

      {order.trackingNumber && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-sm font-medium">رقم التتبع:</span>
              <span className="text-sm ml-2">{order.trackingNumber}</span>
            </div>
            <button
              onClick={() => onPrintLabel(order.id)}
              className="btn btn-secondary text-sm py-1"
              disabled={!order.trackingNumber}
            >
              طباعة الملصق
            </button>
          </div>
        </div>
      )}

      <div className="mt-4 border-t pt-4">
        <div className="flex flex-col md:flex-row gap-2">
          <select
            value={selectedCompany}
            onChange={(e) => setSelectedCompany(e.target.value)}
            className="input md:flex-1"
            disabled={!!order.deliveryCompany || activeCompanies.length === 0}
          >
            <option value="">اختر شركة التوصيل</option>
            {activeCompanies.map(company => (
              <option key={company.id} value={company.id}>
                {company.name}
              </option>
            ))}
          </select>
          <button
            onClick={handleSyncOrder}
            className="btn btn-primary"
            disabled={!selectedCompany || !!order.deliveryCompany}
          >
            مزامنة الطلب
          </button>
        </div>
      </div>
    </div>
  );
}