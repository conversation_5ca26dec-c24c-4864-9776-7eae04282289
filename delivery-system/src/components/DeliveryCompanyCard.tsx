'use client';

import { useState } from 'react';
import Image from 'next/image';
import { DeliveryCompany } from '@/lib/types';

interface DeliveryCompanyCardProps {
  company: DeliveryCompany;
  onToggleActive: (id: string, isActive: boolean) => void;
  onSaveApiKeys: (id: string, apiKeys: Record<string, string>) => void;
}

export default function DeliveryCompanyCard({ 
  company, 
  onToggleActive, 
  onSaveApiKeys 
}: DeliveryCompanyCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>(company.apiKeys || {});

  const handleToggleActive = () => {
    onToggleActive(company.id, !company.isActive);
  };

  const handleInputChange = (key: string, value: string) => {
    setApiKeys(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    onSaveApiKeys(company.id, apiKeys);
    setIsEditing(false);
  };

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="relative w-16 h-16 mr-4 overflow-hidden rounded-lg">
            <Image
              src={company.logo}
              alt={company.name}
              fill
              style={{ objectFit: 'contain' }}
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold">{company.name}</h3>
            <div className="flex items-center mt-1">
              <span className={`badge ${company.isActive ? 'badge-success' : 'badge-error'}`}>
                {company.isActive ? 'مفعل' : 'غير مفعل'}
              </span>
            </div>
          </div>
        </div>
        <div>
          <button
            onClick={handleToggleActive}
            className={`btn ${company.isActive ? 'btn-danger' : 'btn-primary'} ml-2`}
          >
            {company.isActive ? 'تعطيل' : 'تفعيل'}
          </button>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="btn btn-secondary"
          >
            {isEditing ? 'إلغاء' : 'إعداد المفاتيح'}
          </button>
        </div>
      </div>

      {isEditing && (
        <div className="mt-4 border-t pt-4">
          <h4 className="text-md font-medium mb-3">إعداد مفاتيح API</h4>
          <div className="space-y-3">
            {company.requiredKeys.map((keyInfo) => (
              <div key={keyInfo.key}>
                <label htmlFor={`${company.id}-${keyInfo.key}`} className="label">
                  {keyInfo.name}
                </label>
                <input
                  id={`${company.id}-${keyInfo.key}`}
                  type={keyInfo.type}
                  value={apiKeys[keyInfo.key] || ''}
                  onChange={(e) => handleInputChange(keyInfo.key, e.target.value)}
                  placeholder={keyInfo.placeholder}
                  className="input"
                />
              </div>
            ))}
            <div className="flex justify-end mt-4">
              <button
                onClick={handleSave}
                className="btn btn-primary"
              >
                حفظ المفاتيح
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mt-4 grid grid-cols-3 gap-2">
        <div className="text-center p-2 bg-gray-50 rounded">
          <div className="text-sm font-medium">أسعار التوصيل</div>
          <div className={`mt-1 ${company.features.getPrices ? 'text-green-500' : 'text-red-500'}`}>
            {company.features.getPrices ? 'متاح' : 'غير متاح'}
          </div>
        </div>
        <div className="text-center p-2 bg-gray-50 rounded">
          <div className="text-sm font-medium">أرقام التتبع</div>
          <div className={`mt-1 ${company.features.getTrackingNumber ? 'text-green-500' : 'text-red-500'}`}>
            {company.features.getTrackingNumber ? 'متاح' : 'غير متاح'}
          </div>
        </div>
        <div className="text-center p-2 bg-gray-50 rounded">
          <div className="text-sm font-medium">ملصقات الشحن</div>
          <div className={`mt-1 ${company.features.getShippingLabel ? 'text-green-500' : 'text-red-500'}`}>
            {company.features.getShippingLabel ? 'متاح' : 'غير متاح'}
          </div>
        </div>
      </div>
    </div>
  );
}'use client';

import { useState } from 'react';
import Image from 'next/image';
import { DeliveryCompany } from '@/lib/types';

interface DeliveryCompanyCardProps {
  company: DeliveryCompany;
  onToggleActive: (id: string, isActive: boolean) => void;
  onSaveApiKeys: (id: string, apiKeys: Record<string, string>) => void;
}

export default function DeliveryCompanyCard({ 
  company, 
  onToggleActive, 
  onSaveApiKeys 
}: DeliveryCompanyCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>(company.apiKeys || {});

  const handleToggleActive = () => {
    onToggleActive(company.id, !company.isActive);
  };

  const handleInputChange = (key: string, value: string) => {
    setApiKeys(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    onSaveApiKeys(company.id, apiKeys);
    setIsEditing(false);
  };

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="relative w-16 h-16 mr-4 overflow-hidden rounded-lg">
            <Image
              src={company.logo}
              alt={company.name}
              fill
              style={{ objectFit: 'contain' }}
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold">{company.name}</h3>
            <div className="flex items-center mt-1">
              <span className={`badge ${company.isActive ? 'badge-success' : 'badge-error'}`}>
                {company.isActive ? 'مفعل' : 'غير مفعل'}
              </span>
            </div>
          </div>
        </div>
        <div>
          <button
            onClick={handleToggleActive}
            className={`btn ${company.isActive ? 'btn-danger' : 'btn-primary'} ml-2`}
          >
            {company.isActive ? 'تعطيل' : 'تفعيل'}
          </button>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="btn btn-secondary"
          >
            {isEditing ? 'إلغاء' : 'إعداد المفاتيح'}
          </button>
        </div>
      </div>

      {isEditing && (
        <div className="mt-4 border-t pt-4">
          <h4 className="text-md font-medium mb-3">إعداد مفاتيح API</h4>
          <div className="space-y-3">
            {company.requiredKeys.map((keyInfo) => (
              <div key={keyInfo.key}>
                <label htmlFor={`${company.id}-${keyInfo.key}`} className="label">
                  {keyInfo.name}
                </label>
                <input
                  id={`${company.id}-${keyInfo.key}`}
                  type={keyInfo.type}
                  value={apiKeys[keyInfo.key] || ''}
                  onChange={(e) => handleInputChange(keyInfo.key, e.target.value)}
                  placeholder={keyInfo.placeholder}
                  className="input"
                />
              </div>
            ))}
            <div className="flex justify-end mt-4">
              <button
                onClick={handleSave}
                className="btn btn-primary"
              >
                حفظ المفاتيح
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mt-4 grid grid-cols-3 gap-2">
        <div className="text-center p-2 bg-gray-50 rounded">
          <div className="text-sm font-medium">أسعار التوصيل</div>
          <div className={`mt-1 ${company.features.getPrices ? 'text-green-500' : 'text-red-500'}`}>
            {company.features.getPrices ? 'متاح' : 'غير متاح'}
          </div>
        </div>
        <div className="text-center p-2 bg-gray-50 rounded">
          <div className="text-sm font-medium">أرقام التتبع</div>
          <div className={`mt-1 ${company.features.getTrackingNumber ? 'text-green-500' : 'text-red-500'}`}>
            {company.features.getTrackingNumber ? 'متاح' : 'غير متاح'}
          </div>
        </div>
        <div className="text-center p-2 bg-gray-50 rounded">
          <div className="text-sm font-medium">ملصقات الشحن</div>
          <div className={`mt-1 ${company.features.getShippingLabel ? 'text-green-500' : 'text-red-500'}`}>
            {company.features.getShippingLabel ? 'متاح' : 'غير متاح'}
          </div>
        </div>
      </div>
    </div>
  );
}'use client';

import { useState } from 'react';
import Image from 'next/image';
import { DeliveryCompany } from '@/lib/types';

interface DeliveryCompanyCardProps {
  company: DeliveryCompany;
  onToggleActive: (id: string, isActive: boolean) => void;
  onSaveApiKeys: (id: string, apiKeys: Record<string, string>) => void;
}

export default function DeliveryCompanyCard({ 
  company, 
  onToggleActive, 
  onSaveApiKeys 
}: DeliveryCompanyCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>(company.apiKeys || {});

  const handleToggleActive = () => {
    onToggleActive(company.id, !company.isActive);
  };

  const handleInputChange = (key: string, value: string) => {
    setApiKeys(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    onSaveApiKeys(company.id, apiKeys);
    setIsEditing(false);
  };

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="relative w-16 h-16 mr-4 overflow-hidden rounded-lg">
            <Image
              src={company.logo}
              alt={company.name}
              fill
              style={{ objectFit: 'contain' }}
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold">{company.name}</h3>
            <div className="flex items-center mt-1">
              <span className={`badge ${company.isActive ? 'badge-success' : 'badge-error'}`}>
                {company.isActive ? 'مفعل' : 'غير مفعل'}
              </span>
            </div>
          </div>
        </div>
        <div>
          <button
            onClick={handleToggleActive}
            className={`btn ${company.isActive ? 'btn-danger' : 'btn-primary'} ml-2`}
          >
            {company.isActive ? 'تعطيل' : 'تفعيل'}
          </button>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="btn btn-secondary"
          >
            {isEditing ? 'إلغاء' : 'إعداد المفاتيح'}
          </button>
        </div>
      </div>

      {isEditing && (
        <div className="mt-4 border-t pt-4">
          <h4 className="text-md font-medium mb-3">إعداد مفاتيح API</h4>
          <div className="space-y-3">
            {company.requiredKeys.map((keyInfo) => (
              <div key={keyInfo.key}>
                <label htmlFor={`${company.id}-${keyInfo.key}`} className="label">
                  {keyInfo.name}
                </label>
                <input
                  id={`${company.id}-${keyInfo.key}`}
                  type={keyInfo.type}
                  value={apiKeys[keyInfo.key] || ''}
                  onChange={(e) => handleInputChange(keyInfo.key, e.target.value)}
                  placeholder={keyInfo.placeholder}
                  className="input"
                />
              </div>
            ))}
            <div className="flex justify-end mt-4">
              <button
                onClick={handleSave}
                className="btn btn-primary"
              >
                حفظ المفاتيح
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mt-4 grid grid-cols-3 gap-2">
        <div className="text-center p-2 bg-gray-50 rounded">
          <div className="text-sm font-medium">أسعار التوصيل</div>
          <div className={`mt-1 ${company.features.getPrices ? 'text-green-500' : 'text-red-500'}`}>
            {company.features.getPrices ? 'متاح' : 'غير متاح'}
          </div>
        </div>
        <div className="text-center p-2 bg-gray-50 rounded">
          <div className="text-sm font-medium">أرقام التتبع</div>
          <div className={`mt-1 ${company.features.getTrackingNumber ? 'text-green-500' : 'text-red-500'}`}>
            {company.features.getTrackingNumber ? 'متاح' : 'غير متاح'}
          </div>
        </div>
        <div className="text-center p-2 bg-gray-50 rounded">
          <div className="text-sm font-medium">ملصقات الشحن</div>
          <div className={`mt-1 ${company.features.getShippingLabel ? 'text-green-500' : 'text-red-500'}`}>
            {company.features.getShippingLabel ? 'متاح' : 'غير متاح'}
          </div>
        </div>
      </div>
    </div>
  );
}