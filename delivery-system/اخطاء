Build Error
Failed to compile

Next.js (14.2.30) is outdated (learn more)
./src/app/page.tsx
Error: 
  × the name `<PERSON>` is defined multiple times
    ╭─[/home/<USER>/Desktop/shipping system/delivery-system/src/app/page.tsx:1:1]
  1 │ import Link from 'next/link'
    ·        ──┬─
    ·          ╰── previous definition of `Link` here
  2 │ 
  3 │ export default function Home() {
  4 │   return (
  5 │     <div className="space-y-8">
  6 │       <div className="text-center">
  7 │         <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
  8 │         <p className="text-xl text-gray-600 max-w-3xl mx-auto">
  9 │           نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
 10 │         </p>
 11 │       </div>
 12 │ 
 13 │       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
 14 │         <div className="card hover:shadow-lg transition-shadow">
 15 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
 16 │           <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
 17 │           <Link href="/delivery-companies" className="btn btn-primary block text-center">
 18 │             إدارة شركات التوصيل
 19 │           </Link>
 20 │         </div>
 21 │ 
 22 │         <div className="card hover:shadow-lg transition-shadow">
 23 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
 24 │           <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
 25 │           <Link href="/orders" className="btn btn-primary block text-center">
 26 │             إدارة الطلبات
 27 │           </Link>
 28 │         </div>
 29 │ 
 30 │         <div className="card hover:shadow-lg transition-shadow">
 31 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
 32 │           <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
 33 │           <Link href="/shipping-labels" className="btn btn-primary block text-center">
 34 │             طباعة الملصقات
 35 │           </Link>
 36 │         </div>
 37 │       </div>
 38 │ 
 39 │       <div className="card">
 40 │         <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
 41 │         <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
 42 │           {[
 43 │             'MSM GO', 
 44 │             'NOEST Express', 
 45 │             'ROCKET DELIVERY', 
 46 │             'WORLD EXPRESS', 
 47 │             'Yalidine', 
 48 │             'ZR Express'
 49 │           ].map((company) => (
 50 │             <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
 51 │               <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
 52 │                 {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
 53 │                 <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
 54 │               </div>
 55 │               <span className="text-sm font-medium text-center">{company}</span>
 56 │             </div>
 57 │           ))}
 58 │         </div>
 59 │       </div>
 60 │     </div>
 61 │   )
 62 │ }import Link from 'next/link'
    ·         ──┬─
    ·           ╰── `Link` redefined here
 63 │ 
 64 │ export default function Home() {
 65 │   return (
    ╰────

  × the name `Home` is defined multiple times
    ╭─[/home/<USER>/Desktop/shipping system/delivery-system/src/app/page.tsx:1:1]
  1 │ import Link from 'next/link'
  2 │ 
  3 │ export default function Home() {
    ·                         ──┬─
    ·                           ╰── previous definition of `Home` here
  4 │   return (
  5 │     <div className="space-y-8">
  6 │       <div className="text-center">
  7 │         <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
  8 │         <p className="text-xl text-gray-600 max-w-3xl mx-auto">
  9 │           نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
 10 │         </p>
 11 │       </div>
 12 │ 
 13 │       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
 14 │         <div className="card hover:shadow-lg transition-shadow">
 15 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
 16 │           <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
 17 │           <Link href="/delivery-companies" className="btn btn-primary block text-center">
 18 │             إدارة شركات التوصيل
 19 │           </Link>
 20 │         </div>
 21 │ 
 22 │         <div className="card hover:shadow-lg transition-shadow">
 23 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
 24 │           <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
 25 │           <Link href="/orders" className="btn btn-primary block text-center">
 26 │             إدارة الطلبات
 27 │           </Link>
 28 │         </div>
 29 │ 
 30 │         <div className="card hover:shadow-lg transition-shadow">
 31 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
 32 │           <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
 33 │           <Link href="/shipping-labels" className="btn btn-primary block text-center">
 34 │             طباعة الملصقات
 35 │           </Link>
 36 │         </div>
 37 │       </div>
 38 │ 
 39 │       <div className="card">
 40 │         <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
 41 │         <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
 42 │           {[
 43 │             'MSM GO', 
 44 │             'NOEST Express', 
 45 │             'ROCKET DELIVERY', 
 46 │             'WORLD EXPRESS', 
 47 │             'Yalidine', 
 48 │             'ZR Express'
 49 │           ].map((company) => (
 50 │             <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
 51 │               <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
 52 │                 {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
 53 │                 <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
 54 │               </div>
 55 │               <span className="text-sm font-medium text-center">{company}</span>
 56 │             </div>
 57 │           ))}
 58 │         </div>
 59 │       </div>
 60 │     </div>
 61 │   )
 62 │ }import Link from 'next/link'
 63 │ 
 64 │ export default function Home() {
    ·                         ──┬─
    ·                           ╰── `Home` redefined here
 65 │   return (
 66 │     <div className="space-y-8">
 67 │       <div className="text-center">
    ╰────

  × the name `Link` is defined multiple times
     ╭─[/home/<USER>/Desktop/shipping system/delivery-system/src/app/page.tsx:59:1]
  59 │       </div>
  60 │     </div>
  61 │   )
  62 │ }import Link from 'next/link'
     ·         ──┬─
     ·           ╰── previous definition of `Link` here
  63 │ 
  64 │ export default function Home() {
  65 │   return (
  66 │     <div className="space-y-8">
  67 │       <div className="text-center">
  68 │         <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
  69 │         <p className="text-xl text-gray-600 max-w-3xl mx-auto">
  70 │           نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
  71 │         </p>
  72 │       </div>
  73 │ 
  74 │       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  75 │         <div className="card hover:shadow-lg transition-shadow">
  76 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
  77 │           <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
  78 │           <Link href="/delivery-companies" className="btn btn-primary block text-center">
  79 │             إدارة شركات التوصيل
  80 │           </Link>
  81 │         </div>
  82 │ 
  83 │         <div className="card hover:shadow-lg transition-shadow">
  84 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
  85 │           <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
  86 │           <Link href="/orders" className="btn btn-primary block text-center">
  87 │             إدارة الطلبات
  88 │           </Link>
  89 │         </div>
  90 │ 
  91 │         <div className="card hover:shadow-lg transition-shadow">
  92 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
  93 │           <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
  94 │           <Link href="/shipping-labels" className="btn btn-primary block text-center">
  95 │             طباعة الملصقات
  96 │           </Link>
  97 │         </div>
  98 │       </div>
  99 │ 
 100 │       <div className="card">
 101 │         <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
 102 │         <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
 103 │           {[
 104 │             'MSM GO', 
 105 │             'NOEST Express', 
 106 │             'ROCKET DELIVERY', 
 107 │             'WORLD EXPRESS', 
 108 │             'Yalidine', 
 109 │             'ZR Express'
 110 │           ].map((company) => (
 111 │             <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
 112 │               <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
 113 │                 {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
 114 │                 <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
 115 │               </div>
 116 │               <span className="text-sm font-medium text-center">{company}</span>
 117 │             </div>
 118 │           ))}
 119 │         </div>
 120 │       </div>
 121 │     </div>
 122 │   )
 123 │ }import Link from 'next/link'
     ·         ──┬─
     ·           ╰── `Link` redefined here
 124 │ 
 125 │ export default function Home() {
 126 │   return (
     ╰────

  × the name `Home` is defined multiple times
     ╭─[/home/<USER>/Desktop/shipping system/delivery-system/src/app/page.tsx:1:1]
   1 │ import Link from 'next/link'
   2 │ 
   3 │ export default function Home() {
     ·                         ──┬─
     ·                           ╰── previous definition of `Home` here
   4 │   return (
   5 │     <div className="space-y-8">
   6 │       <div className="text-center">
   7 │         <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
   8 │         <p className="text-xl text-gray-600 max-w-3xl mx-auto">
   9 │           نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
  10 │         </p>
  11 │       </div>
  12 │ 
  13 │       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  14 │         <div className="card hover:shadow-lg transition-shadow">
  15 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
  16 │           <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
  17 │           <Link href="/delivery-companies" className="btn btn-primary block text-center">
  18 │             إدارة شركات التوصيل
  19 │           </Link>
  20 │         </div>
  21 │ 
  22 │         <div className="card hover:shadow-lg transition-shadow">
  23 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
  24 │           <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
  25 │           <Link href="/orders" className="btn btn-primary block text-center">
  26 │             إدارة الطلبات
  27 │           </Link>
  28 │         </div>
  29 │ 
  30 │         <div className="card hover:shadow-lg transition-shadow">
  31 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
  32 │           <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
  33 │           <Link href="/shipping-labels" className="btn btn-primary block text-center">
  34 │             طباعة الملصقات
  35 │           </Link>
  36 │         </div>
  37 │       </div>
  38 │ 
  39 │       <div className="card">
  40 │         <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
  41 │         <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
  42 │           {[
  43 │             'MSM GO', 
  44 │             'NOEST Express', 
  45 │             'ROCKET DELIVERY', 
  46 │             'WORLD EXPRESS', 
  47 │             'Yalidine', 
  48 │             'ZR Express'
  49 │           ].map((company) => (
  50 │             <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
  51 │               <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
  52 │                 {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
  53 │                 <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
  54 │               </div>
  55 │               <span className="text-sm font-medium text-center">{company}</span>
  56 │             </div>
  57 │           ))}
  58 │         </div>
  59 │       </div>
  60 │     </div>
  61 │   )
  62 │ }import Link from 'next/link'
  63 │ 
  64 │ export default function Home() {
  65 │   return (
  66 │     <div className="space-y-8">
  67 │       <div className="text-center">
  68 │         <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
  69 │         <p className="text-xl text-gray-600 max-w-3xl mx-auto">
  70 │           نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
  71 │         </p>
  72 │       </div>
  73 │ 
  74 │       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  75 │         <div className="card hover:shadow-lg transition-shadow">
  76 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
  77 │           <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
  78 │           <Link href="/delivery-companies" className="btn btn-primary block text-center">
  79 │             إدارة شركات التوصيل
  80 │           </Link>
  81 │         </div>
  82 │ 
  83 │         <div className="card hover:shadow-lg transition-shadow">
  84 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
  85 │           <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
  86 │           <Link href="/orders" className="btn btn-primary block text-center">
  87 │             إدارة الطلبات
  88 │           </Link>
  89 │         </div>
  90 │ 
  91 │         <div className="card hover:shadow-lg transition-shadow">
  92 │           <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
  93 │           <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
  94 │           <Link href="/shipping-labels" className="btn btn-primary block text-center">
  95 │             طباعة الملصقات
  96 │           </Link>
  97 │         </div>
  98 │       </div>
  99 │ 
 100 │       <div className="card">
 101 │         <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
 102 │         <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
 103 │           {[
 104 │             'MSM GO', 
 105 │             'NOEST Express', 
 106 │             'ROCKET DELIVERY', 
 107 │             'WORLD EXPRESS', 
 108 │             'Yalidine', 
 109 │             'ZR Express'
 110 │           ].map((company) => (
 111 │             <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
 112 │               <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
 113 │                 {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
 114 │                 <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
 115 │               </div>
 116 │               <span className="text-sm font-medium text-center">{company}</span>
 117 │             </div>
 118 │           ))}
 119 │         </div>
 120 │       </div>
 121 │     </div>
 122 │   )
 123 │ }import Link from 'next/link'
 124 │ 
 125 │ export default function Home() {
     ·                         ──┬─
     ·                           ╰── `Home` redefined here
 126 │   return (
 127 │     <div className="space-y-8">
 128 │       <div className="text-center">
     ╰────

  × the name `default` is exported multiple times
     ╭─[/home/<USER>/Desktop/shipping system/delivery-system/src/app/page.tsx:1:1]
   1 │     import Link from 'next/link'
   2 │     
   3 │ ╭─▶ export default function Home() {
   4 │ │     return (
   5 │ │       <div className="space-y-8">
   6 │ │         <div className="text-center">
   7 │ │           <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
   8 │ │           <p className="text-xl text-gray-600 max-w-3xl mx-auto">
   9 │ │             نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
  10 │ │           </p>
  11 │ │         </div>
  12 │ │   
  13 │ │         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  14 │ │           <div className="card hover:shadow-lg transition-shadow">
  15 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
  16 │ │             <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
  17 │ │             <Link href="/delivery-companies" className="btn btn-primary block text-center">
  18 │ │               إدارة شركات التوصيل
  19 │ │             </Link>
  20 │ │           </div>
  21 │ │   
  22 │ │           <div className="card hover:shadow-lg transition-shadow">
  23 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
  24 │ │             <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
  25 │ │             <Link href="/orders" className="btn btn-primary block text-center">
  26 │ │               إدارة الطلبات
  27 │ │             </Link>
  28 │ │           </div>
  29 │ │   
  30 │ │           <div className="card hover:shadow-lg transition-shadow">
  31 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
  32 │ │             <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
  33 │ │             <Link href="/shipping-labels" className="btn btn-primary block text-center">
  34 │ │               طباعة الملصقات
  35 │ │             </Link>
  36 │ │           </div>
  37 │ │         </div>
  38 │ │   
  39 │ │         <div className="card">
  40 │ │           <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
  41 │ │           <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
  42 │ │             {[
  43 │ │               'MSM GO', 
  44 │ │               'NOEST Express', 
  45 │ │               'ROCKET DELIVERY', 
  46 │ │               'WORLD EXPRESS', 
  47 │ │               'Yalidine', 
  48 │ │               'ZR Express'
  49 │ │             ].map((company) => (
  50 │ │               <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
  51 │ │                 <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
  52 │ │                   {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
  53 │ │                   <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
  54 │ │                 </div>
  55 │ │                 <span className="text-sm font-medium text-center">{company}</span>
  56 │ │               </div>
  57 │ │             ))}
  58 │ │           </div>
  59 │ │         </div>
  60 │ │       </div>
  61 │ │     )
  62 │ ├─▶ }import Link from 'next/link'
     · ╰──── previous exported here
  63 │     
  64 │ ╭─▶ export default function Home() {
  65 │ │     return (
  66 │ │       <div className="space-y-8">
  67 │ │         <div className="text-center">
  68 │ │           <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
  69 │ │           <p className="text-xl text-gray-600 max-w-3xl mx-auto">
  70 │ │             نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
  71 │ │           </p>
  72 │ │         </div>
  73 │ │   
  74 │ │         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  75 │ │           <div className="card hover:shadow-lg transition-shadow">
  76 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
  77 │ │             <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
  78 │ │             <Link href="/delivery-companies" className="btn btn-primary block text-center">
  79 │ │               إدارة شركات التوصيل
  80 │ │             </Link>
  81 │ │           </div>
  82 │ │   
  83 │ │           <div className="card hover:shadow-lg transition-shadow">
  84 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
  85 │ │             <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
  86 │ │             <Link href="/orders" className="btn btn-primary block text-center">
  87 │ │               إدارة الطلبات
  88 │ │             </Link>
  89 │ │           </div>
  90 │ │   
  91 │ │           <div className="card hover:shadow-lg transition-shadow">
  92 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
  93 │ │             <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
  94 │ │             <Link href="/shipping-labels" className="btn btn-primary block text-center">
  95 │ │               طباعة الملصقات
  96 │ │             </Link>
  97 │ │           </div>
  98 │ │         </div>
  99 │ │   
 100 │ │         <div className="card">
 101 │ │           <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
 102 │ │           <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
 103 │ │             {[
 104 │ │               'MSM GO', 
 105 │ │               'NOEST Express', 
 106 │ │               'ROCKET DELIVERY', 
 107 │ │               'WORLD EXPRESS', 
 108 │ │               'Yalidine', 
 109 │ │               'ZR Express'
 110 │ │             ].map((company) => (
 111 │ │               <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
 112 │ │                 <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
 113 │ │                   {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
 114 │ │                   <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
 115 │ │                 </div>
 116 │ │                 <span className="text-sm font-medium text-center">{company}</span>
 117 │ │               </div>
 118 │ │             ))}
 119 │ │           </div>
 120 │ │         </div>
 121 │ │       </div>
 122 │ │     )
 123 │ ├─▶ }import Link from 'next/link'
     · ╰──── exported more than once
 124 │     
 125 │     export default function Home() {
 126 │       return (
     ╰────

Error: 
  ☞ Exported identifiers must be unique

  × the name `default` is exported multiple times
     ╭─[/home/<USER>/Desktop/shipping system/delivery-system/src/app/page.tsx:61:1]
  61 │       )
  62 │     }import Link from 'next/link'
  63 │     
  64 │ ╭─▶ export default function Home() {
  65 │ │     return (
  66 │ │       <div className="space-y-8">
  67 │ │         <div className="text-center">
  68 │ │           <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
  69 │ │           <p className="text-xl text-gray-600 max-w-3xl mx-auto">
  70 │ │             نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
  71 │ │           </p>
  72 │ │         </div>
  73 │ │   
  74 │ │         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  75 │ │           <div className="card hover:shadow-lg transition-shadow">
  76 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
  77 │ │             <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
  78 │ │             <Link href="/delivery-companies" className="btn btn-primary block text-center">
  79 │ │               إدارة شركات التوصيل
  80 │ │             </Link>
  81 │ │           </div>
  82 │ │   
  83 │ │           <div className="card hover:shadow-lg transition-shadow">
  84 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
  85 │ │             <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
  86 │ │             <Link href="/orders" className="btn btn-primary block text-center">
  87 │ │               إدارة الطلبات
  88 │ │             </Link>
  89 │ │           </div>
  90 │ │   
  91 │ │           <div className="card hover:shadow-lg transition-shadow">
  92 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
  93 │ │             <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
  94 │ │             <Link href="/shipping-labels" className="btn btn-primary block text-center">
  95 │ │               طباعة الملصقات
  96 │ │             </Link>
  97 │ │           </div>
  98 │ │         </div>
  99 │ │   
 100 │ │         <div className="card">
 101 │ │           <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
 102 │ │           <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
 103 │ │             {[
 104 │ │               'MSM GO', 
 105 │ │               'NOEST Express', 
 106 │ │               'ROCKET DELIVERY', 
 107 │ │               'WORLD EXPRESS', 
 108 │ │               'Yalidine', 
 109 │ │               'ZR Express'
 110 │ │             ].map((company) => (
 111 │ │               <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
 112 │ │                 <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
 113 │ │                   {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
 114 │ │                   <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
 115 │ │                 </div>
 116 │ │                 <span className="text-sm font-medium text-center">{company}</span>
 117 │ │               </div>
 118 │ │             ))}
 119 │ │           </div>
 120 │ │         </div>
 121 │ │       </div>
 122 │ │     )
 123 │ ├─▶ }import Link from 'next/link'
     · ╰──── previous exported here
 124 │     
 125 │ ╭─▶ export default function Home() {
 126 │ │     return (
 127 │ │       <div className="space-y-8">
 128 │ │         <div className="text-center">
 129 │ │           <h1 className="text-4xl font-bold text-gray-900 mb-4">مرحباً بك في نظام مزامنة شركات التوصيل</h1>
 130 │ │           <p className="text-xl text-gray-600 max-w-3xl mx-auto">
 131 │ │             نظام متكامل لمزامنة طلبات متجرك مع شركات التوصيل، استرداد أسعار التوصيل، أرقام التتبع وملصقات الشحن
 132 │ │           </p>
 133 │ │         </div>
 134 │ │   
 135 │ │         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
 136 │ │           <div className="card hover:shadow-lg transition-shadow">
 137 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">إدارة شركات التوصيل</h2>
 138 │ │             <p className="text-gray-600 mb-4">قم بإعداد وتكوين شركات التوصيل المختلفة وإدارة مفاتيح API الخاصة بها</p>
 139 │ │             <Link href="/delivery-companies" className="btn btn-primary block text-center">
 140 │ │               إدارة شركات التوصيل
 141 │ │             </Link>
 142 │ │           </div>
 143 │ │   
 144 │ │           <div className="card hover:shadow-lg transition-shadow">
 145 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">مزامنة الطلبات</h2>
 146 │ │             <p className="text-gray-600 mb-4">قم بمزامنة طلبات متجرك مع شركات التوصيل واسترداد معلومات الشحن</p>
 147 │ │             <Link href="/orders" className="btn btn-primary block text-center">
 148 │ │               إدارة الطلبات
 149 │ │             </Link>
 150 │ │           </div>
 151 │ │   
 152 │ │           <div className="card hover:shadow-lg transition-shadow">
 153 │ │             <h2 className="text-xl font-semibold text-gray-900 mb-2">طباعة ملصقات الشحن</h2>
 154 │ │             <p className="text-gray-600 mb-4">قم بطباعة ملصقات الشحن للطلبات المزامنة مع شركات التوصيل</p>
 155 │ │             <Link href="/shipping-labels" className="btn btn-primary block text-center">
 156 │ │               طباعة الملصقات
 157 │ │             </Link>
 158 │ │           </div>
 159 │ │         </div>
 160 │ │   
 161 │ │         <div className="card">
 162 │ │           <h2 className="text-2xl font-semibold text-gray-900 mb-4">شركات التوصيل المدعومة</h2>
 163 │ │           <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
 164 │ │             {[
 165 │ │               'MSM GO', 
 166 │ │               'NOEST Express', 
 167 │ │               'ROCKET DELIVERY', 
 168 │ │               'WORLD EXPRESS', 
 169 │ │               'Yalidine', 
 170 │ │               'ZR Express'
 171 │ │             ].map((company) => (
 172 │ │               <div key={company} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg">
 173 │ │                 <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 flex items-center justify-center overflow-hidden">
 174 │ │                   {/* سيتم استبدال هذا بصورة الشركة لاحقاً */}
 175 │ │                   <span className="text-xs text-gray-500">{company.substring(0, 2)}</span>
 176 │ │                 </div>
 177 │ │                 <span className="text-sm font-medium text-center">{company}</span>
 178 │ │               </div>
 179 │ │             ))}
 180 │ │           </div>
 181 │ │         </div>
 182 │ │       </div>
 183 │ │     )
 184 │ ├─▶ }
     · ╰──── exported more than once
     ╰────

Error: 
  ☞ Exported identifiers must be unique